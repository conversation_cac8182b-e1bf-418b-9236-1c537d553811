import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { permissionService } from '@/services';
import type { 
  PermissionDto, 
  CreatePermissionRequest, 
  UpdatePermissionRequest,
  PermissionFilterDto 
} from '@/interfaces';

export const usePermissionStore = defineStore('permission', () => {
  // State
  const permissions = ref<PermissionDto[]>([]);
  const currentPermission = ref<PermissionDto | null>(null);
  const isLoading = ref(false);
  const error = ref<string | null>(null);
  const searchQuery = ref('');
  const filters = ref<PermissionFilterDto>({
    searchQuery: '',
    resource: 'All',
    action: 'All',
    isActive: 'All',
    hasRoles: 'All'
  });
  const pagination = ref({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  });
  const availableResources = ref<string[]>([]);
  const availableActions = ref<string[]>([]);

  // Getters
  const filteredPermissions = computed(() => {
    let filtered = permissions.value;

    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase();
      filtered = filtered.filter(permission => 
        permission.name?.toLowerCase().includes(query) ||
        permission.description?.toLowerCase().includes(query) ||
        permission.resource?.toLowerCase().includes(query) ||
        permission.action?.toLowerCase().includes(query)
      );
    }

    if (filters.value.resource !== 'All') {
      filtered = filtered.filter(permission => permission.resource === filters.value.resource);
    }

    if (filters.value.action !== 'All') {
      filtered = filtered.filter(permission => permission.action === filters.value.action);
    }

    if (filters.value.isActive !== 'All') {
      filtered = filtered.filter(permission => permission.isActive === filters.value.isActive);
    }

    if (filters.value.hasRoles !== 'All') {
      filtered = filtered.filter(permission => {
        const hasRoles = permission.roles && permission.roles.length > 0;
        return filters.value.hasRoles ? hasRoles : !hasRoles;
      });
    }

    return filtered;
  });

  const permissionsByResource = computed(() => {
    const grouped: Record<string, PermissionDto[]> = {};
    permissions.value.forEach(permission => {
      if (!grouped[permission.resource]) {
        grouped[permission.resource] = [];
      }
      grouped[permission.resource].push(permission);
    });
    return grouped;
  });

  const permissionsByStatus = computed(() => {
    return {
      active: permissions.value.filter(p => p.isActive).length,
      inactive: permissions.value.filter(p => !p.isActive).length,
      withRoles: permissions.value.filter(p => p.roles && p.roles.length > 0).length,
      withoutRoles: permissions.value.filter(p => !p.roles || p.roles.length === 0).length,
    };
  });

  const totalPermissions = computed(() => permissions.value.length);
  const activePermissions = computed(() => permissions.value.filter(p => p.isActive).length);

  // Actions
  const fetchPermissions = async (): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      permissions.value = await permissionService.getAllPermissions();
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch permissions';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchPermissionsPaginated = async (page: number = 1, limit: number = 10): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      
      const response = await permissionService.getPermissionsPaginated(page, limit);
      permissions.value = response.permissions;
      pagination.value = {
        page: response.page,
        limit,
        total: response.total,
        totalPages: response.totalPages
      };
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch permissions';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchPermissionById = async (id: string): Promise<PermissionDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const permission = await permissionService.getPermissionById(id);
      currentPermission.value = permission;
      return permission;
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch permission';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const createPermission = async (permissionData: CreatePermissionRequest): Promise<PermissionDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const newPermission = await permissionService.createPermission(permissionData);
      permissions.value.push(newPermission);
      return newPermission;
    } catch (err: any) {
      error.value = err.message || 'Failed to create permission';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const updatePermission = async (id: string, permissionData: UpdatePermissionRequest): Promise<PermissionDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const updatedPermission = await permissionService.updatePermission(id, permissionData);
      
      const index = permissions.value.findIndex(p => p.id === id);
      if (index !== -1) {
        permissions.value[index] = updatedPermission;
      }
      
      if (currentPermission.value?.id === id) {
        currentPermission.value = updatedPermission;
      }
      
      return updatedPermission;
    } catch (err: any) {
      error.value = err.message || 'Failed to update permission';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const deletePermission = async (id: string): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      await permissionService.deletePermission(id);
      
      permissions.value = permissions.value.filter(p => p.id !== id);
      
      if (currentPermission.value?.id === id) {
        currentPermission.value = null;
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to delete permission';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const searchPermissions = async (query: string): Promise<void> => {
    searchQuery.value = query;
    if (query) {
      try {
        isLoading.value = true;
        error.value = null;
        permissions.value = await permissionService.searchPermissions(query);
      } catch (err: any) {
        error.value = err.message || 'Failed to search permissions';
        throw err;
      } finally {
        isLoading.value = false;
      }
    } else {
      await fetchPermissions();
    }
  };

  const filterPermissions = async (filterOptions: PermissionFilterDto): Promise<void> => {
    filters.value = { ...filterOptions };
    try {
      isLoading.value = true;
      error.value = null;
      permissions.value = await permissionService.filterPermissions(filterOptions);
    } catch (err: any) {
      error.value = err.message || 'Failed to filter permissions';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchAvailableResources = async (): Promise<void> => {
    try {
      availableResources.value = await permissionService.getAvailableResources();
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch available resources';
      throw err;
    }
  };

  const fetchAvailableActions = async (): Promise<void> => {
    try {
      availableActions.value = await permissionService.getAvailableActions();
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch available actions';
      throw err;
    }
  };

  const getPermissionsByResource = async (resource: string): Promise<PermissionDto[]> => {
    try {
      return await permissionService.getPermissionsByResource(resource);
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch permissions by resource';
      throw err;
    }
  };

  const togglePermissionStatus = async (id: string): Promise<PermissionDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const updatedPermission = await permissionService.togglePermissionStatus(id);
      
      const index = permissions.value.findIndex(p => p.id === id);
      if (index !== -1) {
        permissions.value[index] = updatedPermission;
      }
      
      if (currentPermission.value?.id === id) {
        currentPermission.value = updatedPermission;
      }
      
      return updatedPermission;
    } catch (err: any) {
      error.value = err.message || 'Failed to toggle permission status';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const bulkCreatePermissions = async (resource: string, actions: string[]): Promise<PermissionDto[]> => {
    try {
      isLoading.value = true;
      error.value = null;
      const newPermissions = await permissionService.bulkCreatePermissions(resource, actions);
      permissions.value.push(...newPermissions);
      return newPermissions;
    } catch (err: any) {
      error.value = err.message || 'Failed to bulk create permissions';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const clearError = (): void => {
    error.value = null;
  };

  const clearCurrentPermission = (): void => {
    currentPermission.value = null;
  };

  const resetFilters = (): void => {
    filters.value = {
      searchQuery: '',
      resource: 'All',
      action: 'All',
      isActive: 'All',
      hasRoles: 'All'
    };
    searchQuery.value = '';
  };

  return {
    // State
    permissions,
    currentPermission,
    isLoading,
    error,
    searchQuery,
    filters,
    pagination,
    availableResources,
    availableActions,
    
    // Getters
    filteredPermissions,
    permissionsByResource,
    permissionsByStatus,
    totalPermissions,
    activePermissions,
    
    // Actions
    fetchPermissions,
    fetchPermissionsPaginated,
    fetchPermissionById,
    createPermission,
    updatePermission,
    deletePermission,
    searchPermissions,
    filterPermissions,
    fetchAvailableResources,
    fetchAvailableActions,
    getPermissionsByResource,
    togglePermissionStatus,
    bulkCreatePermissions,
    clearError,
    clearCurrentPermission,
    resetFilters,
  };
});
