<template>
  <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
    <BreadcrumbsActions :breadcrumbs="roleBreadcrumbs" />

    <!-- Loading State -->
    <div v-if="roleStore.isLoading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-maneb-primary"></div>
      <span class="ml-2 text-gray-600">Loading roles...</span>
    </div>

    <!-- Error State -->
    <div v-else-if="roleStore.error" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">Error loading roles</h3>
          <p class="mt-1 text-sm text-red-700">{{ roleStore.error }}</p>
          <button @click="roleStore.clearError(); loadRoles()" class="mt-2 text-sm text-red-600 hover:text-red-500 underline">
            Try again
          </button>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div v-else>
      <!-- Stats Cards -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-red-50 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-maneb-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Total Roles</p>
              <p class="text-2xl font-semibold text-black">{{ roleStore.totalRoles }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-50 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Approved Roles</p>
              <p class="text-2xl font-semibold text-black">{{ roleStore.approvedRoles }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-50 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">With Permissions</p>
              <p class="text-2xl font-semibold text-black">{{ roleStore.rolesByStatus.withPermissions }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-yellow-50 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Pending Approval</p>
              <p class="text-2xl font-semibold text-black">{{ roleStore.rolesByStatus.unapproved }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Roles Table -->
      <RolesTable
        :roles="roleStore.filteredRoles"
        :isLoading="roleStore.isLoading"
        @create="handleCreateRole"
        @edit="handleEditRole"
        @delete="handleDeleteRole"
        @view-permissions="handleViewPermissions"
        @update-status="handleUpdateStatus"
      />
    </div>

    <!-- Create/Edit Role Modal -->
    <RoleFormModal
      v-if="showRoleModal"
      :role="selectedRole"
      :isEdit="isEditMode"
      @close="closeRoleModal"
      @save="handleSaveRole"
    />

    <!-- Role Permissions Modal -->
    <RolePermissionsModal
      v-if="showPermissionsModal"
      :role="selectedRole"
      @close="closePermissionsModal"
    />

    <Footer />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoleStore } from '@/store/role.store';
import BreadcrumbsActions from '@/components/UI/breadcrumbs/breadcrumbs_actions.vue';
import Footer from '@/components/UI/footers/footer-one.vue';
import RolesTable from '../components/RolesTable.vue';
import RoleFormModal from '../components/RoleFormModal.vue';
import RolePermissionsModal from '../components/RolePermissionsModal.vue';
import type { RoleDto, RecordStatus } from '@/interfaces';

// Store
const roleStore = useRoleStore();

// Component state
const showRoleModal = ref(false);
const showPermissionsModal = ref(false);
const selectedRole = ref<RoleDto | null>(null);
const isEditMode = ref(false);

// Breadcrumbs
const roleBreadcrumbs = [
  { name: 'Admin', href: '/admin', current: false },
  { name: 'User Management', href: '/admin/user-management', current: false },
  { name: 'Roles', href: '/admin/user-management/roles', current: true },
];

// Methods
const loadRoles = async () => {
  try {
    await roleStore.fetchRoles();
  } catch (error) {
    console.error('Failed to load roles:', error);
  }
};

const handleCreateRole = () => {
  selectedRole.value = null;
  isEditMode.value = false;
  showRoleModal.value = true;
};

const handleEditRole = (role: RoleDto) => {
  selectedRole.value = role;
  isEditMode.value = true;
  showRoleModal.value = true;
};

const handleDeleteRole = async (role: RoleDto) => {
  if (!role.id) return;

  if (confirm(`Are you sure you want to delete the role "${role.name}"?`)) {
    try {
      await roleStore.deleteRole(role.id);
      // Show success notification
    } catch (error) {
      console.error('Failed to delete role:', error);
      // Show error notification
    }
  }
};

const handleViewPermissions = (role: RoleDto) => {
  selectedRole.value = role;
  showPermissionsModal.value = true;
};

const handleUpdateStatus = async (role: RoleDto, status: RecordStatus) => {
  if (!role.id) return;

  try {
    await roleStore.updateRoleStatus(role.id, status);
    // Show success notification
  } catch (error) {
    console.error('Failed to update role status:', error);
    // Show error notification
  }
};

const handleSaveRole = async (roleData: any) => {
  try {
    if (isEditMode.value && selectedRole.value?.id) {
      await roleStore.updateRole(selectedRole.value.id, roleData);
    } else {
      await roleStore.createRole(roleData);
    }
    closeRoleModal();
    // Show success notification
  } catch (error) {
    console.error('Failed to save role:', error);
    // Show error notification
  }
};

const closeRoleModal = () => {
  showRoleModal.value = false;
  selectedRole.value = null;
  isEditMode.value = false;
};

const closePermissionsModal = () => {
  showPermissionsModal.value = false;
  selectedRole.value = null;
};

// Lifecycle
onMounted(() => {
  loadRoles();
});
</script>

<style scoped>
/* MANEB Theme Colors */
.text-maneb-primary {
  color: #a12c2c;
}

.bg-maneb-primary {
  background-color: #a12c2c;
}

.border-maneb-primary {
  border-color: #a12c2c;
}
</style>
