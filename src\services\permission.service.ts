import apiClient from './api-client';
import type { 
  PermissionDto, 
  CreatePermissionRequest, 
  UpdatePermissionRequest,
  PermissionFilterDto 
} from '@/interfaces';

export class PermissionService {
  /**
   * Get all permissions
   */
  async getAllPermissions(): Promise<PermissionDto[]> {
    try {
      return await apiClient.get<PermissionDto[]>('/api/Permission');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch permissions');
    }
  }

  /**
   * Get permission by ID
   */
  async getPermissionById(id: string): Promise<PermissionDto> {
    try {
      return await apiClient.get<PermissionDto>(`/api/Permission/${id}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch permission');
    }
  }

  /**
   * Create new permission
   */
  async createPermission(permissionData: CreatePermissionRequest): Promise<PermissionDto> {
    try {
      return await apiClient.post<PermissionDto>('/api/Permission', permissionData);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to create permission');
    }
  }

  /**
   * Update existing permission
   */
  async updatePermission(id: string, permissionData: UpdatePermissionRequest): Promise<PermissionDto> {
    try {
      return await apiClient.put<PermissionDto>(`/api/Permission/${id}`, permissionData);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to update permission');
    }
  }

  /**
   * Delete permission
   */
  async deletePermission(id: string): Promise<void> {
    try {
      await apiClient.delete(`/api/Permission/${id}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to delete permission');
    }
  }

  /**
   * Get permissions with pagination
   */
  async getPermissionsPaginated(page: number = 1, limit: number = 10): Promise<{
    permissions: PermissionDto[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      const allPermissions = await this.getAllPermissions();
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      
      return {
        permissions: allPermissions.slice(startIndex, endIndex),
        total: allPermissions.length,
        page,
        totalPages: Math.ceil(allPermissions.length / limit)
      };
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch paginated permissions');
    }
  }

  /**
   * Search permissions
   */
  async searchPermissions(query: string): Promise<PermissionDto[]> {
    try {
      const permissions = await this.getAllPermissions();
      const searchTerm = query.toLowerCase();
      
      return permissions.filter(permission => 
        permission.name?.toLowerCase().includes(searchTerm) ||
        permission.description?.toLowerCase().includes(searchTerm) ||
        permission.resource?.toLowerCase().includes(searchTerm) ||
        permission.action?.toLowerCase().includes(searchTerm)
      );
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to search permissions');
    }
  }

  /**
   * Filter permissions
   */
  async filterPermissions(filters: PermissionFilterDto): Promise<PermissionDto[]> {
    try {
      let permissions = await this.getAllPermissions();

      if (filters.searchQuery) {
        const searchTerm = filters.searchQuery.toLowerCase();
        permissions = permissions.filter(permission => 
          permission.name?.toLowerCase().includes(searchTerm) ||
          permission.description?.toLowerCase().includes(searchTerm) ||
          permission.resource?.toLowerCase().includes(searchTerm) ||
          permission.action?.toLowerCase().includes(searchTerm)
        );
      }

      if (filters.resource && filters.resource !== 'All') {
        permissions = permissions.filter(permission => permission.resource === filters.resource);
      }

      if (filters.action && filters.action !== 'All') {
        permissions = permissions.filter(permission => permission.action === filters.action);
      }

      if (filters.isActive !== undefined && filters.isActive !== 'All') {
        permissions = permissions.filter(permission => permission.isActive === filters.isActive);
      }

      if (filters.hasRoles !== undefined && filters.hasRoles !== 'All') {
        permissions = permissions.filter(permission => {
          const hasRoles = permission.roles && permission.roles.length > 0;
          return filters.hasRoles ? hasRoles : !hasRoles;
        });
      }

      return permissions;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to filter permissions');
    }
  }

  /**
   * Get permissions by resource
   */
  async getPermissionsByResource(resource: string): Promise<PermissionDto[]> {
    try {
      const permissions = await this.getAllPermissions();
      return permissions.filter(permission => permission.resource === resource);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch permissions by resource');
    }
  }

  /**
   * Get available resources
   */
  async getAvailableResources(): Promise<string[]> {
    try {
      const permissions = await this.getAllPermissions();
      const resources = [...new Set(permissions.map(p => p.resource))];
      return resources.sort();
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch available resources');
    }
  }

  /**
   * Get available actions
   */
  async getAvailableActions(): Promise<string[]> {
    try {
      const permissions = await this.getAllPermissions();
      const actions = [...new Set(permissions.map(p => p.action))];
      return actions.sort();
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch available actions');
    }
  }

  /**
   * Toggle permission active status
   */
  async togglePermissionStatus(id: string): Promise<PermissionDto> {
    try {
      return await apiClient.put<PermissionDto>(`/api/Permission/${id}/toggle-status`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to toggle permission status');
    }
  }

  /**
   * Bulk create permissions for a resource
   */
  async bulkCreatePermissions(resource: string, actions: string[]): Promise<PermissionDto[]> {
    try {
      const permissionRequests = actions.map(action => ({
        name: `${action} ${resource}`,
        description: `Allows ${action.toLowerCase()} operations on ${resource.toLowerCase()}`,
        resource,
        action,
        isActive: true
      }));

      return await apiClient.post<PermissionDto[]>('/api/Permission/bulk', permissionRequests);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to bulk create permissions');
    }
  }
}

export const permissionService = new PermissionService();
