// API Response Types
export interface ApiResponse<T = any> {
  data?: T;
  message?: string;
  success: boolean;
  errors?: string[];
}

// Authentication Types
export interface LoginModel {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface AuthResponse {
  token: string;
  refreshToken: string;
  user: UserDto;
  expiresAt: string;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

// Education Management System Types (based on actual API)
export type UserStatus = number; // 1 = Student, 2 = Admin
export type Gender = 'Male' | 'Female' | 'Other';

export interface UserDto {
  // System fields (from actual API)
  id?: string;
  createdBy?: string;
  dateCreated?: Date;
  status?: RecordStatus; // Updated to use RecordStatus enum
  isDeleted?: boolean;

  // Required user info (from actual API)
  firstName: string;
  lastName: string;

  // Personal details (from actual API)
  gender?: Gender;
  idNumber?: string;
  idType?: string;
  dateOfBirth?: Date;
  email?: string;
  userName?: string;
  fullName?: string; // Read-only computed field

  // Role relationship (from actual API)
  roleId?: string;
  role?: RoleDto;
}

export interface CreateUserRequest extends Omit<UserDto, 'id' | 'createdBy' | 'dateCreated' | 'fullName'> {}
export interface UpdateUserRequest extends Partial<CreateUserRequest> {}

// API Client Types
export interface ApiClientConfig {
  baseURL: string;
  timeout?: number;
}

export interface RequestConfig {
  headers?: Record<string, string>;
  params?: Record<string, any>;
}

// Export table interfaces
export * from './table';

// Grading System Types
export type GradeLevel = 'A' | 'B' | 'C' | 'D' | 'F';
export type ScoreType = 'first' | 'verify' | 'remark' | 'final' | 'regression' | 'absent';
export type ExamType = 'PLCE' | 'MSCE' | 'TTC' | 'JCE' | 'PSLCE';

export interface GradeDto {
  id?: string;
  createdBy?: string;
  dateCreated?: Date;
  isDeleted?: boolean;

  // Grade details
  studentId: string;
  subjectId: string;
  paperId: string;
  year: number;
  startScore: number;
  endScore: number;
  assignedGrade: GradeLevel;
  failResult: boolean;
}

export interface SubjectDto {
  id?: string;
  createdBy?: string;
  dateCreated?: Date;
  isDeleted?: boolean;

  // Subject details
  name: string;
  code: string;
  description?: string;
  isActive: boolean;
}

export interface PaperDto {
  id?: string;
  createdBy?: string;
  dateCreated?: Date;
  isDeleted?: boolean;

  // Paper details
  subjectId: string;
  name: string;
  code: string;
  description?: string;
  paperNumber: number;
  duration?: number; // Duration in minutes
  maxMarks?: number;
  isActive: boolean;
}

export interface GradeBoundaryDto {
  id?: string;
  createdBy?: string;
  dateCreated?: Date;
  isDeleted?: boolean;

  // Grade boundary details
  examType: ExamType;
  subjectId: string;
  paperId: string;
  year: number;
  gradeLevel: GradeLevel;
  minScore: number;
  maxScore: number;
  description?: string;
  isActive: boolean;
}

export interface GradeConfigurationDto {
  id?: string;
  createdBy?: string;
  dateCreated?: Date;
  isDeleted?: boolean;

  // Configuration details
  year: number;
  isActive: boolean;
  description?: string;
  gradeBoundaries: GradeBoundaryDto[];
}

// Request types for grading system
export interface CreateGradeRequest extends Omit<GradeDto, 'id' | 'createdBy' | 'dateCreated'> {}
export interface UpdateGradeRequest extends Partial<CreateGradeRequest> {}

export interface CreateSubjectRequest extends Omit<SubjectDto, 'id' | 'createdBy' | 'dateCreated'> {}
export interface UpdateSubjectRequest extends Partial<CreateSubjectRequest> {}

export interface CreatePaperRequest extends Omit<PaperDto, 'id' | 'createdBy' | 'dateCreated'> {}
export interface UpdatePaperRequest extends Partial<CreatePaperRequest> {}

export interface CreateGradeBoundaryRequest extends Omit<GradeBoundaryDto, 'id' | 'createdBy' | 'dateCreated'> {}
export interface UpdateGradeBoundaryRequest extends Partial<CreateGradeBoundaryRequest> {}

export interface CreateGradeConfigurationRequest extends Omit<GradeConfigurationDto, 'id' | 'createdBy' | 'dateCreated'> {}
export interface UpdateGradeConfigurationRequest extends Partial<CreateGradeConfigurationRequest> {}

// Comprehensive Grade Boundary Interfaces
export interface GradeBoundarySet {
  gradeLevel: GradeLevel;
  minScore: number;
  maxScore: number;
  description?: string;
}

export interface ComprehensiveGradeBoundaryRequest {
  examType: ExamType;
  subjectId: string;
  paperId: string;
  year: number;
  gradeBoundaries: GradeBoundarySet[];
  isActive?: boolean;
}

export interface BulkGradeBoundaryResponse {
  success: boolean;
  createdBoundaries: GradeBoundaryDto[];
  errors?: string[];
}

// Results Module Interfaces
export interface StudentResultDto {
  id?: string;
  studentId: string;
  subjectId: string;
  paperId: string;
  year: number;
  score: number;
  calculatedGrade: GradeLevel;
  passStatus: boolean;
  dateCalculated?: Date;
}

// Certificate Module Interfaces
export interface CertificateSubjectResult {
  subjectId: string;
  subjectName: string;
  subjectCode: string;
  papers: CertificatePaperResult[];
  overallGrade: GradeLevel;
  overallPassStatus: boolean;
}

export interface CertificatePaperResult {
  paperId: string;
  paperName: string;
  paperCode: string;
  score: number;
  grade: GradeLevel;
  passStatus: boolean;
}

export interface StudentCertificateDto {
  studentId: string;
  studentName?: string;
  examNumber?: string;
  year: number;
  examSession: string; // e.g., "2024 Main Session"
  subjects: CertificateSubjectResult[];
  overallPerformance: {
    totalSubjects: number;
    passedSubjects: number;
    failedSubjects: number;
    overallGrade: GradeLevel;
    passRate: number;
  };
  certificateNumber: string;
  issueDate: Date;
  issuedBy: string;
}

export interface ResultsFilterDto {
  searchQuery?: string;
  year?: number | 'All';
  subjectId?: string | 'All';
  paperId?: string | 'All';
  gradeLevel?: GradeLevel | 'All';
  passStatus?: boolean | 'All';
}

// Examination-level Results Interfaces
export interface ExaminationResultDto {
  id?: string;
  studentId: string;
  examNumber?: string;
  level: ExamType; // Examination level
  year: number;
  averageScore: number; // Calculated across all subjects
  averageGrade: GradeLevel; // Based on average score and grade boundaries
  totalSubjects: number;
  passedSubjects: number;
  failedSubjects: number;
  passRate: number; // Percentage of subjects passed
  status: 'Complete' | 'Incomplete' | 'Pending'; // Examination status
  dateCalculated?: Date;
  subjects: StudentSubjectResult[]; // All subjects taken in this examination
}

export interface StudentSubjectResult {
  subjectId: string;
  subjectName: string;
  subjectCode: string;
  papers: StudentPaperResult[];
  averageScore: number; // Average across all papers in this subject
  overallGrade: GradeLevel;
  passStatus: boolean;
}

export interface StudentPaperResult {
  paperId: string;
  paperName: string;
  paperCode: string;
  score: number;
  grade: GradeLevel;
  passStatus: boolean;
  scoreType: ScoreType;
}

export interface ExaminationResultsFilterDto {
  searchQuery?: string;
  year?: number | 'All';
  level?: ExamType | 'All';
  gradeLevel?: GradeLevel | 'All';
  passStatus?: boolean | 'All';
  status?: 'Complete' | 'Incomplete' | 'Pending' | 'All';
}

// User Management System Types (Based on actual API)
// Role Management
export type RecordStatus = 'Unapproved' | 'Approved' | 'SecondApproved' | 'Rejected';
export type PermissionAction = 'Login' | 'Logout' | 'Update' | 'Delete' | 'Authorise' | 'Approve' | 'Reject' | 'SecondApprove' | 'All' | 'View' | 'ViewAll' | 'Create';

export interface RoleDto {
  id?: string;
  createdBy?: string;
  dateCreated?: Date;
  status?: RecordStatus;
  isDeleted?: boolean;

  // Role details
  name: string;
  rolePermissions?: RolePermissionDto[];
}

export interface CreateRoleRequest extends Omit<RoleDto, 'id' | 'createdBy' | 'dateCreated' | 'rolePermissions'> {}

export interface UpdateRoleRequest extends Partial<CreateRoleRequest> {}

// Role Permission Management (Junction table)
export interface RolePermissionDto {
  id?: string;
  createdBy?: string;
  dateCreated?: Date;
  status?: RecordStatus;
  isDeleted?: boolean;

  // Permission details
  sectionID: string; // Required - references Section
  roleId: string; // Required - references Role
  action: PermissionAction;
  canAccess: boolean;
}

export interface CreateRolePermissionRequest extends Omit<RolePermissionDto, 'id' | 'createdBy' | 'dateCreated'> {}

export interface UpdateRolePermissionRequest extends Partial<CreateRolePermissionRequest> {}

// Section Management (Organizational Structure)
export interface SectionDto {
  id?: string;
  createdBy?: string;
  dateCreated?: Date;
  status?: RecordStatus;
  isDeleted?: boolean;

  // Section details
  name: string;
  controller?: string; // API controller name
  area?: string; // API area name
}

export interface CreateSectionRequest extends Omit<SectionDto, 'id' | 'createdBy' | 'dateCreated'> {}

export interface UpdateSectionRequest extends Partial<CreateSectionRequest> {}

// Filter and Search Types
export interface RoleFilterDto {
  searchQuery?: string;
  status?: RecordStatus | 'All';
  hasPermissions?: boolean | 'All';
}

export interface RolePermissionFilterDto {
  searchQuery?: string;
  roleId?: string | 'All';
  sectionId?: string | 'All';
  action?: PermissionAction | 'All';
  canAccess?: boolean | 'All';
  status?: RecordStatus | 'All';
}

export interface SectionFilterDto {
  searchQuery?: string;
  controller?: string | 'All';
  area?: string | 'All';
  status?: RecordStatus | 'All';
}

// Bulk operations
export interface BulkRolePermissionRequest {
  roleId: string;
  permissions: CreateRolePermissionRequest[];
}

export interface AssignRoleToUserRequest {
  userId: string;
  roleId: string;
}