import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { roleService } from '@/services';
import type { 
  RoleDto, 
  CreateRoleRequest, 
  UpdateRoleRequest, 
  AssignPermissionsToRoleRequest,
  RoleFilterDto 
} from '@/interfaces';

export const useRoleStore = defineStore('role', () => {
  // State
  const roles = ref<RoleDto[]>([]);
  const currentRole = ref<RoleDto | null>(null);
  const isLoading = ref(false);
  const error = ref<string | null>(null);
  const searchQuery = ref('');
  const filters = ref<RoleFilterDto>({
    searchQuery: '',
    isActive: 'All',
    hasPermissions: 'All',
    userCount: 'All'
  });
  const pagination = ref({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  });

  // Getters
  const filteredRoles = computed(() => {
    let filtered = roles.value;

    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase();
      filtered = filtered.filter(role => 
        role.name?.toLowerCase().includes(query) ||
        role.description?.toLowerCase().includes(query)
      );
    }

    if (filters.value.isActive !== 'All') {
      filtered = filtered.filter(role => role.isActive === filters.value.isActive);
    }

    if (filters.value.hasPermissions !== 'All') {
      filtered = filtered.filter(role => {
        const hasPerms = role.permissions && role.permissions.length > 0;
        return filters.value.hasPermissions ? hasPerms : !hasPerms;
      });
    }

    if (filters.value.userCount !== 'All') {
      filtered = filtered.filter(role => {
        const count = role.userCount || 0;
        switch (filters.value.userCount) {
          case 'None': return count === 0;
          case 'Some': return count > 0 && count <= 10;
          case 'Many': return count > 10;
          default: return true;
        }
      });
    }

    return filtered;
  });

  const rolesByStatus = computed(() => {
    return {
      active: roles.value.filter(r => r.isActive).length,
      inactive: roles.value.filter(r => !r.isActive).length,
      withPermissions: roles.value.filter(r => r.permissions && r.permissions.length > 0).length,
      withoutPermissions: roles.value.filter(r => !r.permissions || r.permissions.length === 0).length,
    };
  });

  const totalRoles = computed(() => roles.value.length);
  const activeRoles = computed(() => roles.value.filter(r => r.isActive).length);

  // Actions
  const fetchRoles = async (): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      roles.value = await roleService.getAllRoles();
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch roles';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchRolesPaginated = async (page: number = 1, limit: number = 10): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      
      const response = await roleService.getRolesPaginated(page, limit);
      roles.value = response.roles;
      pagination.value = {
        page: response.page,
        limit,
        total: response.total,
        totalPages: response.totalPages
      };
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch roles';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchRoleById = async (id: string): Promise<RoleDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const role = await roleService.getRoleById(id);
      currentRole.value = role;
      return role;
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch role';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const createRole = async (roleData: CreateRoleRequest): Promise<RoleDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const newRole = await roleService.createRole(roleData);
      roles.value.push(newRole);
      return newRole;
    } catch (err: any) {
      error.value = err.message || 'Failed to create role';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const updateRole = async (id: string, roleData: UpdateRoleRequest): Promise<RoleDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const updatedRole = await roleService.updateRole(id, roleData);
      
      const index = roles.value.findIndex(r => r.id === id);
      if (index !== -1) {
        roles.value[index] = updatedRole;
      }
      
      if (currentRole.value?.id === id) {
        currentRole.value = updatedRole;
      }
      
      return updatedRole;
    } catch (err: any) {
      error.value = err.message || 'Failed to update role';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const deleteRole = async (id: string): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      await roleService.deleteRole(id);
      
      roles.value = roles.value.filter(r => r.id !== id);
      
      if (currentRole.value?.id === id) {
        currentRole.value = null;
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to delete role';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const assignPermissionsToRole = async (request: AssignPermissionsToRoleRequest): Promise<RoleDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const updatedRole = await roleService.assignPermissionsToRole(request);
      
      const index = roles.value.findIndex(r => r.id === request.roleId);
      if (index !== -1) {
        roles.value[index] = updatedRole;
      }
      
      if (currentRole.value?.id === request.roleId) {
        currentRole.value = updatedRole;
      }
      
      return updatedRole;
    } catch (err: any) {
      error.value = err.message || 'Failed to assign permissions to role';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const removePermissionsFromRole = async (roleId: string, permissionIds: string[]): Promise<RoleDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const updatedRole = await roleService.removePermissionsFromRole(roleId, permissionIds);
      
      const index = roles.value.findIndex(r => r.id === roleId);
      if (index !== -1) {
        roles.value[index] = updatedRole;
      }
      
      if (currentRole.value?.id === roleId) {
        currentRole.value = updatedRole;
      }
      
      return updatedRole;
    } catch (err: any) {
      error.value = err.message || 'Failed to remove permissions from role';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const searchRoles = async (query: string): Promise<void> => {
    searchQuery.value = query;
    if (query) {
      try {
        isLoading.value = true;
        error.value = null;
        roles.value = await roleService.searchRoles(query);
      } catch (err: any) {
        error.value = err.message || 'Failed to search roles';
        throw err;
      } finally {
        isLoading.value = false;
      }
    } else {
      await fetchRoles();
    }
  };

  const filterRoles = async (filterOptions: RoleFilterDto): Promise<void> => {
    filters.value = { ...filterOptions };
    try {
      isLoading.value = true;
      error.value = null;
      roles.value = await roleService.filterRoles(filterOptions);
    } catch (err: any) {
      error.value = err.message || 'Failed to filter roles';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const toggleRoleStatus = async (id: string): Promise<RoleDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const updatedRole = await roleService.toggleRoleStatus(id);
      
      const index = roles.value.findIndex(r => r.id === id);
      if (index !== -1) {
        roles.value[index] = updatedRole;
      }
      
      if (currentRole.value?.id === id) {
        currentRole.value = updatedRole;
      }
      
      return updatedRole;
    } catch (err: any) {
      error.value = err.message || 'Failed to toggle role status';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const clearError = (): void => {
    error.value = null;
  };

  const clearCurrentRole = (): void => {
    currentRole.value = null;
  };

  const resetFilters = (): void => {
    filters.value = {
      searchQuery: '',
      isActive: 'All',
      hasPermissions: 'All',
      userCount: 'All'
    };
    searchQuery.value = '';
  };

  return {
    // State
    roles,
    currentRole,
    isLoading,
    error,
    searchQuery,
    filters,
    pagination,
    
    // Getters
    filteredRoles,
    rolesByStatus,
    totalRoles,
    activeRoles,
    
    // Actions
    fetchRoles,
    fetchRolesPaginated,
    fetchRoleById,
    createRole,
    updateRole,
    deleteRole,
    assignPermissionsToRole,
    removePermissionsFromRole,
    searchRoles,
    filterRoles,
    toggleRoleStatus,
    clearError,
    clearCurrentRole,
    resetFilters,
  };
});
